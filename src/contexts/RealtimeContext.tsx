import React, { createContext, useContext, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { MOTO_TAXI_QUERY_KEYS } from '@/hooks/useMotoTaxi';
import { PHOTO_QUERY_KEYS } from '@/hooks/usePhotos';
import { BEACH_LOCATION_QUERY_KEYS } from '@/hooks/useBeachLocations';
import { PROFILE_QUERY_KEYS } from '@/hooks/useProfile';

interface RealtimeContextType {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (context === undefined) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};

interface RealtimeProviderProps {
  children: React.ReactNode;
}

export const RealtimeProvider: React.FC<RealtimeProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('connecting');

  useEffect(() => {
    // Set up real-time subscriptions
    const subscriptions: any[] = [];

    // Moto taxi drivers subscription
    const driversSubscription = supabase
      .channel('moto_taxi_drivers_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'moto_taxi_drivers'
        },
        (payload) => {
          console.log('Driver change:', payload);
          // Invalidate all driver queries
          queryClient.invalidateQueries({
            queryKey: MOTO_TAXI_QUERY_KEYS.all
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to driver changes');
          setConnectionStatus('connected');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Driver subscription error:', err);
          setConnectionStatus('error');
        } else if (status === 'TIMED_OUT') {
          console.error('Driver subscription timed out');
          setConnectionStatus('error');
        } else if (status === 'CLOSED') {
          console.log('Driver subscription closed');
          setConnectionStatus('disconnected');
        }
      });

    subscriptions.push(driversSubscription);

    // User photos subscription
    const photosSubscription = supabase
      .channel('user_photos_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_photos'
        },
        (payload) => {
          console.log('Photo change:', payload);
          // Invalidate photo queries
          queryClient.invalidateQueries({
            queryKey: PHOTO_QUERY_KEYS.all
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to photo changes');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Photo subscription error:', err);
          setConnectionStatus('error');
        } else if (status === 'TIMED_OUT') {
          console.error('Photo subscription timed out');
          setConnectionStatus('error');
        } else if (status === 'CLOSED') {
          console.log('Photo subscription closed');
          setConnectionStatus('disconnected');
        }
      });

    subscriptions.push(photosSubscription);

    // Photo likes subscription
    const likesSubscription = supabase
      .channel('photo_likes_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'photo_likes'
        },
        (payload) => {
          console.log('Like change:', payload);
          // Invalidate photo queries to refresh like counts
          queryClient.invalidateQueries({
            queryKey: PHOTO_QUERY_KEYS.featured()
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to like changes');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Like subscription error:', err);
          setConnectionStatus('error');
        } else if (status === 'TIMED_OUT') {
          console.error('Like subscription timed out');
          setConnectionStatus('error');
        } else if (status === 'CLOSED') {
          console.log('Like subscription closed');
          setConnectionStatus('disconnected');
        }
      });

    subscriptions.push(likesSubscription);

    // Beach locations subscription
    const locationsSubscription = supabase
      .channel('beach_locations_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'beach_locations'
        },
        (payload) => {
          console.log('Location change:', payload);
          // Invalidate location queries
          queryClient.invalidateQueries({
            queryKey: BEACH_LOCATION_QUERY_KEYS.all
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to location changes');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Location subscription error:', err);
          setConnectionStatus('error');
        } else if (status === 'TIMED_OUT') {
          console.error('Location subscription timed out');
          setConnectionStatus('error');
        } else if (status === 'CLOSED') {
          console.log('Location subscription closed');
          setConnectionStatus('disconnected');
        }
      });

    subscriptions.push(locationsSubscription);

    // User profile subscription (only if user is logged in)
    let profileSubscription: any = null;
    if (user) {
      profileSubscription = supabase
        .channel(`profile_${user.id}_realtime`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profiles',
            filter: `id=eq.${user.id}`
          },
          (payload) => {
            console.log('Profile change:', payload);
            // Invalidate profile queries
            queryClient.invalidateQueries({
              queryKey: PROFILE_QUERY_KEYS.profile(user.id)
            });
          }
        )
        .subscribe((status, err) => {
          if (status === 'SUBSCRIBED') {
            console.log('Subscribed to profile changes');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('Profile subscription error:', err);
            setConnectionStatus('error');
          } else if (status === 'TIMED_OUT') {
            console.error('Profile subscription timed out');
            setConnectionStatus('error');
          } else if (status === 'CLOSED') {
            console.log('Profile subscription closed');
            setConnectionStatus('disconnected');
          }
        });

      subscriptions.push(profileSubscription);
    }

    // Monitor connection status
    const checkConnection = () => {
      const activeChannels = supabase.realtime.channels.filter(channel =>
        channel.state === 'joined' || channel.state === 'joining'
      );
      const hasActiveChannels = activeChannels.length > 0;

      if (hasActiveChannels) {
        // Only set to connected if we're not already in an error state
        if (connectionStatus !== 'error') {
          setConnectionStatus('connected');
        }
      } else {
        setConnectionStatus('disconnected');
      }
    };

    // Check connection status periodically
    const connectionInterval = setInterval(checkConnection, 5000);

    // Initial check after a short delay to allow subscriptions to establish
    setTimeout(checkConnection, 2000);

    // Cleanup function
    return () => {
      clearInterval(connectionInterval);
      subscriptions.forEach(subscription => {
        if (subscription) {
          subscription.unsubscribe();
        }
      });
      setConnectionStatus('disconnected');
    };
  }, [user, queryClient]);

  // Handle connection errors through subscription callbacks
  // Note: Error handling is now done in individual subscription callbacks

  const value: RealtimeContextType = {
    isConnected: connectionStatus === 'connected',
    connectionStatus,
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
    </RealtimeContext.Provider>
  );
};

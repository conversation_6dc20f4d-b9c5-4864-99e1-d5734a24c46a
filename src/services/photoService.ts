import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type UserPhoto = Tables<'user_photos'>;
export type UserPhotoInsert = TablesInsert<'user_photos'>;
export type UserPhotoUpdate = TablesUpdate<'user_photos'>;
export type PhotoLike = Tables<'photo_likes'>;

export interface PhotoWithLikes extends UserPhoto {
  photo_likes: PhotoLike[];
  user_profile?: {
    display_name: string | null;
    avatar_url: string | null;
  };
}

export const photoService = {
  // Get all approved photos with likes and user info
  async getFeaturedPhotos(): Promise<PhotoWithLikes[]> {
    // First, get the photos with likes
    const { data: photos, error: photosError } = await supabase
      .from('user_photos')
      .select(`
        *,
        photo_likes (*)
      `)
      .eq('is_approved', true)
      .order('created_at', { ascending: false });

    if (photosError) {
      throw new Error(`Failed to fetch photos: ${photosError.message}`);
    }

    if (!photos || photos.length === 0) {
      return [];
    }

    // Get unique user IDs
    const userIds = [...new Set(photos.map(photo => photo.user_id))];

    // Fetch profiles for these users
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, display_name, avatar_url')
      .in('id', userIds);

    if (profilesError) {
      console.warn('Failed to fetch profiles:', profilesError.message);
    }

    // Create a map of profiles by user ID
    const profilesMap = new Map(
      (profiles || []).map(profile => [profile.id, profile])
    );

    // Combine photos with profile data
    return photos.map(photo => ({
      ...photo,
      user_profile: profilesMap.get(photo.user_id) || null
    })) as PhotoWithLikes[];
  },

  // Get user's own photos
  async getUserPhotos(userId: string): Promise<UserPhoto[]> {
    const { data, error } = await supabase
      .from('user_photos')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch user photos: ${error.message}`);
    }

    return data || [];
  },

  // Upload a photo to storage
  async uploadPhoto(file: File, userId: string): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/${Date.now()}.${fileExt}`;

    const { error: uploadError } = await supabase.storage
      .from('user-photos')
      .upload(fileName, file);

    if (uploadError) {
      throw new Error(`Failed to upload photo: ${uploadError.message}`);
    }

    const { data } = supabase.storage
      .from('user-photos')
      .getPublicUrl(fileName);

    return data.publicUrl;
  },

  // Create a new photo record
  async createPhoto(photo: UserPhotoInsert): Promise<UserPhoto> {
    const { data, error } = await supabase
      .from('user_photos')
      .insert(photo)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create photo: ${error.message}`);
    }

    return data;
  },

  // Update a photo
  async updatePhoto(id: string, updates: UserPhotoUpdate): Promise<UserPhoto> {
    const { data, error } = await supabase
      .from('user_photos')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update photo: ${error.message}`);
    }

    return data;
  },

  // Delete a photo
  async deletePhoto(id: string, userId: string): Promise<void> {
    // First get the photo to get the image URL
    const { data: photo } = await supabase
      .from('user_photos')
      .select('image_url')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (photo?.image_url) {
      // Extract file path from URL
      const url = new URL(photo.image_url);
      const filePath = url.pathname.split('/').slice(-2).join('/');
      
      // Delete from storage
      await supabase.storage
        .from('user-photos')
        .remove([filePath]);
    }

    // Delete from database
    const { error } = await supabase
      .from('user_photos')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete photo: ${error.message}`);
    }
  },

  // Like a photo
  async likePhoto(photoId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('photo_likes')
      .insert({ photo_id: photoId, user_id: userId });

    if (error) {
      throw new Error(`Failed to like photo: ${error.message}`);
    }

    // Update likes count
    await this.updateLikesCount(photoId);
  },

  // Unlike a photo
  async unlikePhoto(photoId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('photo_likes')
      .delete()
      .eq('photo_id', photoId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to unlike photo: ${error.message}`);
    }

    // Update likes count
    await this.updateLikesCount(photoId);
  },

  // Update likes count for a photo
  async updateLikesCount(photoId: string): Promise<void> {
    const { count } = await supabase
      .from('photo_likes')
      .select('*', { count: 'exact', head: true })
      .eq('photo_id', photoId);

    await supabase
      .from('user_photos')
      .update({ likes_count: count || 0 })
      .eq('id', photoId);
  },

  // Check if user has liked a photo
  async hasUserLikedPhoto(photoId: string, userId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('photo_likes')
      .select('id')
      .eq('photo_id', photoId)
      .eq('user_id', userId)
      .single();

    return !error && !!data;
  },

  // Subscribe to photo changes
  subscribeToPhotos(callback: (payload: any) => void) {
    return supabase
      .channel('user_photos_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_photos'
        },
        callback
      )
      .subscribe();
  },

  // Subscribe to photo likes changes
  subscribeToPhotoLikes(callback: (payload: any) => void) {
    return supabase
      .channel('photo_likes_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'photo_likes'
        },
        callback
      )
      .subscribe();
  }
};
